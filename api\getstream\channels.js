/**
 * GetStream Channels API Route
 *
 * This API route creates and manages GetStream channels.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
// For Vercel deployment, use GETSTREAM_API_KEY and GETSTREAM_API_SECRET
// For local development, use VITE_GETSTREAM_API_KEY and GETSTREAM_API_SECRET (server-only)
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY; // SECURITY: VITE_GETSTREAM_API_KEY is safe (public key)
const apiSecret = process.env.GETSTREAM_API_SECRET || process.env.VITE_GETSTREAM_API_SECRET; // TEMPORARY: Vercel uses VITE_ prefix

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // SECURITY: Never use VITE_ prefix for secrets
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Log environment variables for debugging
console.log('API Key available:', !!apiKey);
console.log('API Secret available:', !!apiSecret);

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if API key and secret are available
    if (!apiKey || !apiSecret || !serverClient) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Get the task ID, title, members, and user ID from the request body
    const { taskId, taskTitle, members, userId } = req.body;

    if (!taskId || !taskTitle || !userId) {
      return res.status(400).json({ error: 'Task ID, title, and user ID are required' });
    }

    console.log('Creating channel for task:', taskId, 'by user:', userId);

    // Validate that the user has permission to create a channel for this task
    // and that all members belong to the same organization
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        user_id,
        assigned_to,
        organization_id,
        visibility
      `)
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      console.error('Task not found or error:', taskError);
      return res.status(404).json({ error: 'Task not found' });
    }

    // Get the requesting user's profile
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('organization_id, role, account_type')
      .eq('id', userId)
      .single();

    if (userError || !userProfile) {
      console.error('User profile not found:', userError);
      return res.status(403).json({ error: 'User not authorized' });
    }

    // Validate organization access
    if (task.organization_id && userProfile.organization_id !== task.organization_id) {
      console.error('User organization mismatch:', userProfile.organization_id, 'vs task org:', task.organization_id);
      return res.status(403).json({ error: 'Access denied: Not a member of task organization' });
    }

    // Validate that all provided members belong to the same organization
    if (members && Array.isArray(members) && members.length > 0) {
      const { data: memberProfiles, error: memberError } = await supabase
        .from('profiles')
        .select('id, organization_id')
        .in('id', members);

      if (memberError) {
        console.error('Error fetching member profiles:', memberError);
        return res.status(500).json({ error: 'Failed to validate members' });
      }

      // Check that all members belong to the same organization as the task
      const invalidMembers = memberProfiles.filter(profile =>
        task.organization_id && profile.organization_id !== task.organization_id
      );

      if (invalidMembers.length > 0) {
        console.error('Invalid members found:', invalidMembers);
        return res.status(403).json({ error: 'Some members do not belong to the task organization' });
      }
    }

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    // Create users if they don't exist
    if (channelMembers.length > 0) {
      try {
        // Create user objects for each member
        const userObjects = channelMembers.map(userId => ({
          id: userId,
          name: userId,
          role: 'user'
        }));

        // Upsert users to ensure they exist
        await serverClient.upsertUsers(userObjects);
        console.log(`Created ${userObjects.length} users for channel`);
      } catch (error) {
        console.error('Error creating users:', error);
        // Continue even if user creation fails
      }
    }

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members by replacing the entire member list
      console.log('Channel already exists, updating membership:', channelId);

      // Get current members
      const currentMembers = Object.keys(channel.state?.members || {});
      console.log(`Current members: ${currentMembers.join(', ')}`);
      console.log(`Target members: ${channelMembers.join(', ')}`);

      // Find members to remove (current members not in target list)
      const membersToRemove = currentMembers.filter(member => !channelMembers.includes(member));

      // Find members to add (target members not in current list)
      const membersToAdd = channelMembers.filter(member => !currentMembers.includes(member));

      // Remove members who should no longer have access
      if (membersToRemove.length > 0) {
        await channel.removeMembers(membersToRemove);
        console.log(`Removed ${membersToRemove.length} members from channel:`, membersToRemove.join(', '));
      }

      // Add new members
      if (membersToAdd.length > 0) {
        await channel.addMembers(membersToAdd);
        console.log(`Added ${membersToAdd.length} members to channel:`, membersToAdd.join(', '));
      }

      if (membersToRemove.length === 0 && membersToAdd.length === 0) {
        console.log('No membership changes needed for channel:', channelId);
      }

      return res.status(200).json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create a new channel
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId,
        organization_id: task.organization_id, // Store organization ID in channel metadata
        created_by_id: 'system' // Required for server-side auth
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      return res.status(200).json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    return res.status(500).json({ error: 'Failed to create/update channel' });
  }
}
