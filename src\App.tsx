
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useParams, useLocation } from "react-router-dom";
import { Suspense, useEffect, useState } from "react";

import MobileAppLayout from "@/layouts/MobileAppLayout";
import GetStreamChatList from "@/pages/mobile/GetStreamChatList";
import GetStreamChatView from "@/pages/mobile/GetStreamChatView";
import PWARoutes from "@/routes/PWARoutes";
import { isPWA } from "@/utils/pwa-utils";
import { AuthProvider } from "./contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Dashboard from "./pages/Dashboard";
import Tasks from "./pages/Tasks";
import TaskWrapper from "./pages/TaskWrapper";
import EnhancedTaskWrapper from "./pages/EnhancedTaskWrapper";
import PostTask from "./pages/PostTask";
import Profile from "./pages/Profile";
import OrganizationSetup from "./pages/OrganizationSetup";
import AcceptInvitation from "./pages/AcceptInvitation";
import InvitationConfirmation from "./pages/InvitationConfirmation";
import SetAdminRole from "./pages/SetAdminRole";

import Login from "./pages/Login";
import Register from "./pages/Register";
import HowItWorks from "./pages/HowItWorks";
import Plans from "./pages/Plans";
import Help from "./pages/Help";
import Contact from "./pages/Contact";
import MessagesDirect from "./pages/MessagesDirect";
import Notifications from "./pages/Notifications";
import Payments from "./pages/Payments";


import AdminUsers from "./pages/AdminUsers";
import AdminTasks from "./pages/AdminTasks";
import OrganizationManagement from "./pages/admin/OrganizationManagement";

import OrganizationDashboard from "./pages/OrganizationDashboard";
import RouteExplorer from "./pages/RouteExplorer";
import OrganizationUsersRedirect from "./pages/OrganizationUsersRedirect";
import StripeConnectPage from "./pages/StripeConnectPage";
import OrganizationInvoices from "./pages/organization/Invoices";
import TestTaskInvoiceFlow from "./pages/TestTaskInvoiceFlow";
import EmergencyTaskActions from "./pages/EmergencyTaskActions";
import InternalTask from "./pages/InternalTask";
import TestInternalTaskActions from "./pages/TestInternalTaskActions";

// New role-based components
import RoleProtectedRoute from "./components/auth/RoleProtectedRoute";
import SupplierProtectedRoute from "./components/auth/SupplierProtectedRoute";
import SiteAdminDashboard from "./pages/admin/SiteAdminDashboard";
import RoleManagement from "./pages/admin/RoleManagement";
import SecurityDashboard from "./pages/admin/SecurityDashboard";
import AccessDenied from "./pages/AccessDenied";


import AssignedTasks from "./pages/AssignedTasks";
import TermsOfService from "./pages/TermsOfService";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import SchoolResources from "./pages/SchoolResources";
import SupplierSignup from "./pages/SupplierSignup";
import SupplierResources from "./pages/SupplierResources";
import SupplierFAQ from "./pages/SupplierFAQ";

// Test Components
import TestChatSection from "./components/dashboard/TestChatSection";
import InternalTaskChatTester from "./components/dashboard/InternalTaskChatTester";


const queryClient = new QueryClient();

// Component to redirect from /tasks/:id to /tasks/enhanced/:id
const TaskRedirect = () => {
  const { id } = useParams();
  const location = useLocation();
  return <Navigate to={`/tasks/enhanced/${id}${location.search}`} replace />;
};

// Component to redirect from /mobile/chat/:threadId to /mobile/stream-chat/:threadId
const ChatRedirect = () => {
  const { threadId } = useParams();
  const location = useLocation();
  return <Navigate to={`/mobile/stream-chat/${threadId}${location.search}`} replace />;
};

const AppWithProviders = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <NotificationProvider>
          <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/tasks" element={
            <SupplierProtectedRoute>
              <Tasks />
            </SupplierProtectedRoute>
          } />
          <Route path="/tasks/create" element={<PostTask />} />
          <Route path="/tasks/my-tasks" element={<Dashboard />} />
          <Route path="/tasks/assigned" element={<AssignedTasks />} />
          {/* Redirect from original Task page to Enhanced Task page */}
          <Route path="/tasks/:id" element={<TaskRedirect />} />

          {/* Enhanced Task page with new architecture */}
          <Route path="/tasks/enhanced/:id" element={<EnhancedTaskWrapper />} />

          {/* Redirect from /enhancedtask/:id to /tasks/enhanced/:id */}
          <Route path="/enhancedtask/:id" element={<TaskRedirect />} />
          <Route path="/post-task" element={<PostTask />} />
          <Route path="/profile/:id" element={<Profile />} />
          <Route path="/organisation/setup" element={<OrganizationSetup />} />
          <Route path="/supplier/onboarding" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/SupplierOnboarding')))}
            </React.Suspense>
          } />

          {/* Organisation Management Routes - Only for organisation admins */}
          <Route
            path="/organisation/dashboard"
            element={
              <RoleProtectedRoute requiredPermission="manage_organizations">
                <OrganizationDashboard />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/organisation/users"
            element={
              <RoleProtectedRoute requiredPermission="manage_organizations">
                <OrganizationUsersRedirect />
              </RoleProtectedRoute>
            }
          /> {/* Redirect old path */}

          <Route
            path="/organisation/settings"
            element={
              <Navigate to="/organisation/dashboard?tab=settings" replace />
            }
          />
          <Route
            path="/organisation/invoices"
            element={
              <RoleProtectedRoute requiredPermission="manage_payments">
                <OrganizationInvoices />
              </RoleProtectedRoute>
            }
          />

          {/* Site Admin Routes */}
          <Route
            path="/admin/site"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <SiteAdminDashboard />
              </RoleProtectedRoute>
            }
          />

          <Route
            path="/admin/roles"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <RoleManagement />
              </RoleProtectedRoute>
            }
          />

          <Route
            path="/admin/security"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <SecurityDashboard />
              </RoleProtectedRoute>
            }
          />


          <Route
            path="/admin/users"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <AdminUsers />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks"
            element={
              <RoleProtectedRoute requiredPermission="manage_tasks">
                <AdminTasks />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/organisations"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <OrganizationManagement />
              </RoleProtectedRoute>
            }
          />



          {/* Authentication Routes */}
          <Route path="/invitation/accept" element={<AcceptInvitation />} />
          <Route path="/invitation-confirmation" element={<InvitationConfirmation />} />
          <Route path="/set-admin-role" element={<SetAdminRole />} />

          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ForgotPassword')))}
            </React.Suspense>
          } />
          {/* Auth callback handler - processes all Supabase auth redirects */}
          <Route path="/auth/callback" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/AuthCallback')))}
            </React.Suspense>
          } />

          {/* Password reset routes */}
          <Route path="/reset-password" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPassword')))}
            </React.Suspense>
          } />

          {/* Password reset confirmation page */}
          <Route path="/reset-password/confirm" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPasswordConfirm')))}
            </React.Suspense>
          } />

          {/* Direct password reset page - handles token directly */}
          <Route path="/reset-password/direct" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPasswordDirect')))}
            </React.Suspense>
          } />

          {/* Public Routes */}
          <Route path="/how-it-works" element={<HowItWorks />} />
          <Route path="/plans" element={<Plans />} />
          <Route path="/help" element={<Help />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />

          {/* Resource Pages */}
          <Route path="/school-resources" element={<SchoolResources />} />
          <Route path="/supplier-signup" element={<SupplierSignup />} />
          <Route path="/supplier-resources" element={<SupplierResources />} />
          <Route path="/supplier-faq" element={<SupplierFAQ />} />

          {/* User Routes */}
          {/* IMPORTANT: All chat functionality should use GetStream implementation via the Dashboard messages tab */}
          <Route path="/messages" element={<Navigate to="/dashboard?tab=messages" replace />} />
          <Route path="/notifications" element={<Notifications />} />
          <Route
            path="/payments"
            element={
              <RoleProtectedRoute requiredPermission="manage_payments">
                <Payments />
              </RoleProtectedRoute>
            }
          />
          <Route path="/stripe-connect" element={<StripeConnectPage />} />

          {/* Access Denied Page */}
          <Route path="/access-denied" element={<AccessDenied />} />

          {/* Mobile App Routes */}
          <Route path="/mobile" element={<MobileAppLayout />}>
            <Route path="chats" element={<GetStreamChatList />} />
            <Route path="stream-chat/:channelId" element={<GetStreamChatView />} />
          </Route>




          {/* Debug Routes - Development Only */}
          {process.env.NODE_ENV === 'development' && (
            <>
              <Route path="/test-component/:id" element={<TestInternalTaskActions />} />
            </>
          )}

          {/* Test Chat Route - Development Only */}
          {process.env.NODE_ENV === 'development' && (
            <Route path="/test-chat" element={
              <div className="container mx-auto p-6 space-y-6">
                <TestChatSection />
                <InternalTaskChatTester />
              </div>
            } />
          )}

          {/* Emergency Routes - Admin Only */}
          <Route path="/emergency/task/:id" element={
            <RoleProtectedRoute requireSiteAdmin>
              <EmergencyTaskActions />
            </RoleProtectedRoute>
          } />
          <Route path="/internal-task/:id" element={<InternalTask />} />

          {/* Catch-all Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>

          {/* Mobile navigation is now handled by MobileAppLayout */}
        </NotificationProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

const App = () => {
  const [isPwaMode, setIsPwaMode] = useState(false);

  // Check if running as PWA
  useEffect(() => {
    setIsPwaMode(isPWA());
  }, []);

  return (
    <BrowserRouter>
      {isPwaMode ? (
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <AuthProvider>
              <NotificationProvider>
                <Suspense fallback={
                  <div className="flex items-center justify-center h-screen">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                }>
                  <PWARoutes />
                </Suspense>
              </NotificationProvider>
            </AuthProvider>
          </TooltipProvider>
        </QueryClientProvider>
      ) : (
        <AppWithProviders />
      )}
    </BrowserRouter>
  );
};

export default App;
