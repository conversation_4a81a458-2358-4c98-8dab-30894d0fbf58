{"timestamp": "2025-05-26T10:07:48.214Z", "summary": {"secrets": "PASS", "dependencies": "FAIL", "customPatterns": "FAIL"}, "details": {"secrets": {"success": true, "output": ""}, "dependencies": {"auditResult": {"success": false, "error": "Command failed: npm audit --json", "output": "{\n  \"auditReportVersion\": 2,\n  \"vulnerabilities\": {\n    \"@vercel/gatsby-plugin-vercel-builder\": {\n      \"name\": \"@vercel/gatsby-plugin-vercel-builder\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        \"esbuild\"\n      ],\n      \"effects\": [\n        \"@vercel/static-build\"\n      ],\n      \"range\": \"*\",\n      \"nodes\": [\n        \"node_modules/@vercel/gatsby-plugin-vercel-builder\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/node\": {\n      \"name\": \"@vercel/node\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        \"esbuild\",\n        \"path-to-regexp\",\n        \"undici\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \">=2.3.1\",\n      \"nodes\": [\n        \"node_modules/@vercel/node\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/remix-builder\": {\n      \"name\": \"@vercel/remix-builder\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        \"path-to-regexp\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \"<=2.0.3 || >=5.2.4\",\n      \"nodes\": [\n        \"node_modules/@vercel/remix-builder\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/static-build\": {\n      \"name\": \"@vercel/static-build\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        \"@vercel/gatsby-plugin-vercel-builder\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \">=1.3.0\",\n      \"nodes\": [\n        \"node_modules/@vercel/static-build\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"esbuild\": {\n      \"name\": \"esbuild\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1102341,\n          \"name\": \"esbuild\",\n          \"dependency\": \"esbuild\",\n          \"title\": \"esbuild enables any website to send any requests to the development server and read the response\",\n          \"url\": \"https://github.com/advisories/GHSA-67mh-4wv8-2f99\",\n          \"severity\": \"moderate\",\n          \"cwe\": [\n            \"CWE-346\"\n          ],\n          \"cvss\": {\n            \"score\": 5.3,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N\"\n          },\n          \"range\": \"<=0.24.2\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/gatsby-plugin-vercel-builder\",\n        \"@vercel/node\",\n        \"vite\"\n      ],\n      \"range\": \"<=0.24.2\",\n      \"nodes\": [\n        \"node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/esbuild\",\n        \"node_modules/@vercel/node/node_modules/esbuild\",\n        \"node_modules/esbuild\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"lovable-tagger\": {\n      \"name\": \"lovable-tagger\",\n      \"severity\": \"moderate\",\n      \"isDirect\": true,\n      \"via\": [\n        \"vite\"\n      ],\n      \"effects\": [],\n      \"range\": \"*\",\n      \"nodes\": [\n        \"node_modules/lovable-tagger\"\n      ],\n      \"fixAvailable\": false\n    },\n    \"path-to-regexp\": {\n      \"name\": \"path-to-regexp\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1101846,\n          \"name\": \"path-to-regexp\",\n          \"dependency\": \"path-to-regexp\",\n          \"title\": \"path-to-regexp outputs backtracking regular expressions\",\n          \"url\": \"https://github.com/advisories/GHSA-9wv6-86v2-598j\",\n          \"severity\": \"high\",\n          \"cwe\": [\n            \"CWE-1333\"\n          ],\n          \"cvss\": {\n            \"score\": 7.5,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H\"\n          },\n          \"range\": \">=4.0.0 <6.3.0\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/node\",\n        \"@vercel/remix-builder\"\n      ],\n      \"range\": \"4.0.0 - 6.2.2\",\n      \"nodes\": [\n        \"node_modules/@vercel/node/node_modules/path-to-regexp\",\n        \"node_modules/@vercel/remix-builder/node_modules/path-to-regexp\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"undici\": {\n      \"name\": \"undici\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1101610,\n          \"name\": \"undici\",\n          \"dependency\": \"undici\",\n          \"title\": \"Use of Insufficiently Random Values in undici\",\n          \"url\": \"https://github.com/advisories/GHSA-c76h-2ccp-4975\",\n          \"severity\": \"moderate\",\n          \"cwe\": [\n            \"CWE-330\"\n          ],\n          \"cvss\": {\n            \"score\": 6.8,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:N\"\n          },\n          \"range\": \">=4.5.0 <5.28.5\"\n        },\n        {\n          \"source\": 1104501,\n          \"name\": \"undici\",\n          \"dependency\": \"undici\",\n          \"title\": \"undici Denial of Service attack via bad certificate data\",\n          \"url\": \"https://github.com/advisories/GHSA-cxrh-j4jr-qwg3\",\n          \"severity\": \"low\",\n          \"cwe\": [\n            \"CWE-401\"\n          ],\n          \"cvss\": {\n            \"score\": 3.1,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L\"\n          },\n          \"range\": \"<5.29.0\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/node\"\n      ],\n      \"range\": \"<=5.28.5\",\n      \"nodes\": [\n        \"node_modules/undici\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"vercel\": {\n      \"name\": \"vercel\",\n      \"severity\": \"high\",\n      \"isDirect\": true,\n      \"via\": [\n        \"@vercel/node\",\n        \"@vercel/remix-builder\",\n        \"@vercel/static-build\"\n      ],\n      \"effects\": [],\n      \"range\": \">=25.2.1\",\n      \"nodes\": [\n        \"node_modules/vercel\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"vite\": {\n      \"name\": \"vite\",\n      \"severity\": \"moderate\",\n      \"isDirect\": true,\n      \"via\": [\n        \"esbuild\"\n      ],\n      \"effects\": [\n        \"lovable-tagger\"\n      ],\n      \"range\": \"0.11.0 - 6.1.6\",\n      \"nodes\": [\n        \"node_modules/vite\"\n      ],\n      \"fixAvailable\": false\n    }\n  },\n  \"metadata\": {\n    \"vulnerabilities\": {\n      \"info\": 0,\n      \"low\": 0,\n      \"moderate\": 6,\n      \"high\": 4,\n      \"critical\": 0,\n      \"total\": 10\n    },\n    \"dependencies\": {\n      \"prod\": 668,\n      \"dev\": 434,\n      \"optional\": 142,\n      \"peer\": 1,\n      \"peerOptional\": 0,\n      \"total\": 1149\n    }\n  }\n}\n"}, "auditCiResult": {"success": false, "error": "Command failed: npx audit-ci --moderate --report --output-format json", "output": "{\n  \"advisories\": {\n    \"@vercel/gatsby-plugin-vercel-builder\": {\n      \"name\": \"@vercel/gatsby-plugin-vercel-builder\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        \"esbuild\"\n      ],\n      \"effects\": [\n        \"@vercel/static-build\"\n      ],\n      \"range\": \"*\",\n      \"nodes\": [\n        \"node_modules/@vercel/gatsby-plugin-vercel-builder\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/node\": {\n      \"name\": \"@vercel/node\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        \"esbuild\",\n        \"path-to-regexp\",\n        \"undici\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \">=2.3.1\",\n      \"nodes\": [\n        \"node_modules/@vercel/node\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/remix-builder\": {\n      \"name\": \"@vercel/remix-builder\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        \"path-to-regexp\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \"<=2.0.3 || >=5.2.4\",\n      \"nodes\": [\n        \"node_modules/@vercel/remix-builder\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"@vercel/static-build\": {\n      \"name\": \"@vercel/static-build\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        \"@vercel/gatsby-plugin-vercel-builder\"\n      ],\n      \"effects\": [\n        \"vercel\"\n      ],\n      \"range\": \">=1.3.0\",\n      \"nodes\": [\n        \"node_modules/@vercel/static-build\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"esbuild\": {\n      \"name\": \"esbuild\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1102341,\n          \"name\": \"esbuild\",\n          \"dependency\": \"esbuild\",\n          \"title\": \"esbuild enables any website to send any requests to the development server and read the response\",\n          \"url\": \"https://github.com/advisories/GHSA-67mh-4wv8-2f99\",\n          \"severity\": \"moderate\",\n          \"cwe\": [\n            \"CWE-346\"\n          ],\n          \"cvss\": {\n            \"score\": 5.3,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N\"\n          },\n          \"range\": \"<=0.24.2\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/gatsby-plugin-vercel-builder\",\n        \"@vercel/node\",\n        \"vite\"\n      ],\n      \"range\": \"<=0.24.2\",\n      \"nodes\": [\n        \"node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/esbuild\",\n        \"node_modules/@vercel/node/node_modules/esbuild\",\n        \"node_modules/esbuild\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"lovable-tagger\": {\n      \"name\": \"lovable-tagger\",\n      \"severity\": \"moderate\",\n      \"isDirect\": true,\n      \"via\": [\n        \"vite\"\n      ],\n      \"effects\": [],\n      \"range\": \"*\",\n      \"nodes\": [\n        \"node_modules/lovable-tagger\"\n      ],\n      \"fixAvailable\": false\n    },\n    \"path-to-regexp\": {\n      \"name\": \"path-to-regexp\",\n      \"severity\": \"high\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1101846,\n          \"name\": \"path-to-regexp\",\n          \"dependency\": \"path-to-regexp\",\n          \"title\": \"path-to-regexp outputs backtracking regular expressions\",\n          \"url\": \"https://github.com/advisories/GHSA-9wv6-86v2-598j\",\n          \"severity\": \"high\",\n          \"cwe\": [\n            \"CWE-1333\"\n          ],\n          \"cvss\": {\n            \"score\": 7.5,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H\"\n          },\n          \"range\": \">=4.0.0 <6.3.0\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/node\",\n        \"@vercel/remix-builder\"\n      ],\n      \"range\": \"4.0.0 - 6.2.2\",\n      \"nodes\": [\n        \"node_modules/@vercel/node/node_modules/path-to-regexp\",\n        \"node_modules/@vercel/remix-builder/node_modules/path-to-regexp\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"undici\": {\n      \"name\": \"undici\",\n      \"severity\": \"moderate\",\n      \"isDirect\": false,\n      \"via\": [\n        {\n          \"source\": 1101610,\n          \"name\": \"undici\",\n          \"dependency\": \"undici\",\n          \"title\": \"Use of Insufficiently Random Values in undici\",\n          \"url\": \"https://github.com/advisories/GHSA-c76h-2ccp-4975\",\n          \"severity\": \"moderate\",\n          \"cwe\": [\n            \"CWE-330\"\n          ],\n          \"cvss\": {\n            \"score\": 6.8,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:N\"\n          },\n          \"range\": \">=4.5.0 <5.28.5\"\n        },\n        {\n          \"source\": 1104501,\n          \"name\": \"undici\",\n          \"dependency\": \"undici\",\n          \"title\": \"undici Denial of Service attack via bad certificate data\",\n          \"url\": \"https://github.com/advisories/GHSA-cxrh-j4jr-qwg3\",\n          \"severity\": \"low\",\n          \"cwe\": [\n            \"CWE-401\"\n          ],\n          \"cvss\": {\n            \"score\": 3.1,\n            \"vectorString\": \"CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L\"\n          },\n          \"range\": \"<5.29.0\"\n        }\n      ],\n      \"effects\": [\n        \"@vercel/node\"\n      ],\n      \"range\": \"<=5.28.5\",\n      \"nodes\": [\n        \"node_modules/undici\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"vercel\": {\n      \"name\": \"vercel\",\n      \"severity\": \"high\",\n      \"isDirect\": true,\n      \"via\": [\n        \"@vercel/node\",\n        \"@vercel/remix-builder\",\n        \"@vercel/static-build\"\n      ],\n      \"effects\": [],\n      \"range\": \">=25.2.1\",\n      \"nodes\": [\n        \"node_modules/vercel\"\n      ],\n      \"fixAvailable\": {\n        \"name\": \"vercel\",\n        \"version\": \"25.2.0\",\n        \"isSemVerMajor\": true\n      }\n    },\n    \"vite\": {\n      \"name\": \"vite\",\n      \"severity\": \"moderate\",\n      \"isDirect\": true,\n      \"via\": [\n        \"esbuild\"\n      ],\n      \"effects\": [\n        \"lovable-tagger\"\n      ],\n      \"range\": \"0.11.0 - 6.1.6\",\n      \"nodes\": [\n        \"node_modules/vite\"\n      ],\n      \"fixAvailable\": false\n    }\n  },\n  \"metadata\": {\n    \"vulnerabilities\": {\n      \"info\": 0,\n      \"low\": 0,\n      \"moderate\": 6,\n      \"high\": 4,\n      \"critical\": 0,\n      \"total\": 10\n    },\n    \"dependencies\": {\n      \"prod\": 668,\n      \"dev\": 434,\n      \"optional\": 142,\n      \"peer\": 1,\n      \"peerOptional\": 0,\n      \"total\": 1149\n    }\n  }\n}\n"}}, "customPatterns": [{"file": ".env", "pattern": "Stripe Test Secret Key", "severity": "HIGH", "matches": ["sk_test_51REKxHAwo0W7IrjoN1jRkmmLyiWGarpzAKItKLmjk9JTRdZPjbZfjZOR7eDcGRStlulfqBNnn7Zf4BRff7JB7f7O003GIQgn5q"]}, {"file": ".env", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyC9rrooRfXGMl5XGYNHFy6rINsRQ7vnYCY"]}, {"file": ".env", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": ".env.local", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"]}, {"file": ".env.local", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk777OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": ".env.production", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": ".vercel\\.env.preview.local", "pattern": "JWT Token", "severity": "HIGH", "matches": ["************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": ".vercel\\output\\static\\assets\\index-BynSfe-i.js", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58", "AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"]}, {"file": ".vercel\\output\\static\\assets\\index-BynSfe-i.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": ".vercel\\output\\static\\assets\\index-BynSfe-i.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password:\"SchoolTest123!\"", "password:\"SupplierTest123!\"", "password:\"}),s.jsxs(\""]}, {"file": ".vercel\\output\\static\\assets\\index-BynSfe-i.js", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["secret:\",v),o(v)}catch(y){console.error(\""]}, {"file": ".vercel\\output\\static\\debug.html", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8KEJub30NhSrbKdXhR0FlFQ0p4U7fNKqmjt0EMSl4ZM"]}, {"file": ".vercel\\output\\static\\index.html", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"]}, {"file": ".vercel\\output\\static\\index.html", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": ".vercel\\output\\static\\test-maps-api.html", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"]}, {"file": "check-anon-access.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "check-api-key-direct.cjs", "pattern": "Stripe Test Secret Key", "severity": "HIGH", "matches": ["sk_test_51REKxHAwo0W7IrjoSqaZGrFgvvXG2QZRl6RN5CVpiQqVIExcX50qW0JrkxePyyTw4ERl89lBj2l0K7YVIm0IGX0w00dbu4fbiS"]}, {"file": "check-database.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "check-function-permissions.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Yd_QVdS_BVMXcj7Wl_ydQtG5RkT0Jp9CKGzJQDGKB-o"]}, {"file": "check-invitation-flow.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'Password123!'", "password: 'Password123!'", "password: 'Password123!'", "password: 'Password123!'"]}, {"file": "check-invitation-status-final.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "check-invitation-status.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "check-payment-intent.cjs", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["Secret = 'pi_3RGOLzAwo0W7Irjo4A5l1FKZ_secret_ISogWt6jOEYrUtL7Wv9rjQ9xo'"]}, {"file": "check-profiles-recursion.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'password123'"]}, {"file": "check-supplier-profile.cjs", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'password123'"]}, {"file": "check-table-security.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "check-user-data.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "docs\\SECURITY_AND_STRESS_TESTING.md", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'password123'", "password: 'password123'", "password: 'password123'"]}, {"file": "fix-invitation-acceptance.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "fix-profile-organization.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "scripts\\.env", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": "scripts\\admin-test.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["Password: 'password123'", "Password: 'password123'"]}, {"file": "scripts\\final-security-check.js", "pattern": "Google API Key", "severity": "HIGH", "matches": ["AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"]}, {"file": "scripts\\install-git-hooks.js", "pattern": "Stripe Test Secret Key", "severity": "HIGH", "matches": ["sk_test_1234567890abcdef1234567890abcdef12345678"]}, {"file": "scripts\\setup-git-hooks.js", "pattern": "Stripe Test Secret Key", "severity": "HIGH", "matches": ["sk_test_1234567890abcdef1234567890abcdef12345678"]}, {"file": "scripts\\test-chat-fixes.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["Password: 'admin123'", "Password: 'supplier123'", "Password: 'supplier123'"]}, {"file": "scripts\\test-getstream-auth.js", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["Secret:', api<PERSON><PERSON><PERSON> ? '", "SECRET:', process.env.GETSTREAM_API_SECRET ? '"]}, {"file": "scripts\\test-getstream-with-real-users.js", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["Secret:', apiSecret.substring(0, 5) + '"]}, {"file": "scripts\\test-getstream.js", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["Secret:', apiSecret.substring(0, 5) + '"]}, {"file": "scripts\\test-support-form-api.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["PASSWORD = 'your_test_password'"]}, {"file": "server\\.env", "pattern": "Stripe Test Secret Key", "severity": "HIGH", "matches": ["sk_test_51REKxHAwo0W7IrjoN1jRkmmLyiWGarpzAKItKLmjk9JTRdZPjbZfjZOR7eDcGRStlulfqBNnn7Zf4BRff7JB7f7O003GIQgn5q"]}, {"file": "server\\.env", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": "src\\components\\stripe\\EnhancedPaymentProcessor.tsx", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["secret:', secret);\r\n        setClientSecret(secret);\r\n      } catch (err) {\r\n        console.error('"]}, {"file": "src\\pages\\Register.tsx", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: \"SchoolTest123!\"", "password: \"SupplierTest123!\""]}, {"file": "src\\pages\\ResetPassword.tsx", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password:', err);\n      setError(err.message || '"]}, {"file": "src\\pages\\ResetPasswordConfirm.tsx", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password:', err);\n      setError(err.message || '"]}, {"file": "src\\pages\\ResetPasswordDirect.tsx", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password:', error);\n        throw error;\n      }\n      \n      console.log('", "password:', err);\n      setError(err.message || '"]}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "pattern": "Hardcoded Secret", "severity": "HIGH", "matches": ["SECRET:', getstreamApiSecret ? '"]}, {"file": "src\\scripts\\comprehensive-security-test.ts", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'SecTest123!'"]}, {"file": "src\\scripts\\simple-security-test.ts", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'SecTest123!'", "password: 'SecTest123!'"]}, {"file": "src\\scripts\\test-role-system.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'Test123!'", "password: 'Test123!'", "password: 'Test123!'", "password: 'Test123!'", "password: 'Test123!'", "password: 'Test123!'"]}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'test-password-123'"]}, {"file": "supabase\\.temp\\pooler-url", "pattern": "PostgreSQL Connection String", "severity": "CRITICAL", "matches": ["postgresql://postgres.qcnotlojmyvpqbbgoxbc:[YOUR-PASSWORD]@aws-0-eu-west-2.pooler.supabase.com:6543/postgres"]}, {"file": "supabase\\functions\\create-test-accounts\\index.ts", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: \"SchoolTest123!\"", "password: \"SupplierTest123!\""]}, {"file": "test-accept-invitation.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "test-app-functionality.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "test-app-functionality.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["Password = 'Password123!'", "Password = 'Password123!'"]}, {"file": "test-cancel-invitation.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "test-core-functionality.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "test-delete-invitation.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "test-direct-cancel.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "test-existing-functionality.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "test-full-flow-with-email-sync.cjs", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["Password = 'password123'"]}, {"file": "test-internal-task-flow-with-users.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": "test-internal-task-flow.cjs", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"]}, {"file": "test-invitation-insert.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Yd_QVdS_BVMXcj7Wl_ydQtG5RkT0Jp9CKGzJQDGKB-o"]}, {"file": "test-notifications.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'AdminTest123!'"]}, {"file": "test-offer-message-notifications.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'AdminTest123!'"]}, {"file": "test-permissions.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8KEJub30NhSrbKdXhR0FlFQ0p4U7fNKqmjt0EMSl4ZM", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}, {"file": "test-profile-creation.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A"]}, {"file": "test-profile-creation.js", "pattern": "Hardcoded Password", "severity": "HIGH", "matches": ["password: 'test-password'", "password: 'test-password'"]}, {"file": "test-supabase.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8KEJub30NhSrbKdXhR0FlFQ0p4U7fNKqmjt0EMSl4ZM"]}, {"file": "test-ui-cancel.js", "pattern": "JWT Token", "severity": "HIGH", "matches": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U"]}]}}