/**
 * GetStream Token API Route
 *
 * This API route generates a token for a GetStream user.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';

// Load environment variables
// SECURITY NOTE: VITE_GETSTREAM_API_KEY is safe for client-side (public key)
// GETSTREAM_API_SECRET must never use VITE_ prefix (server-only secret)
// Try multiple possible environment variable names for compatibility
const apiKey = process.env.GETSTREAM_API_KEY ||
               process.env.VITE_GETSTREAM_API_KEY ||
               process.env.STREAM_API_KEY ||
               process.env.REACT_APP_GETSTREAM_API_KEY;

const apiSecret = process.env.GETSTREAM_API_SECRET ||
                  process.env.STREAM_API_SECRET ||
                  process.env.GETSTREAM_SECRET; // SECURITY: Never use VITE_ prefix for secrets

// Log environment variables for debugging (detailed for Vercel troubleshooting)
console.log('Environment variables check:');
console.log('- GETSTREAM_API_KEY:', !!process.env.GETSTREAM_API_KEY);
console.log('- VITE_GETSTREAM_API_KEY:', !!process.env.VITE_GETSTREAM_API_KEY);
console.log('- STREAM_API_KEY:', !!process.env.STREAM_API_KEY);
console.log('- REACT_APP_GETSTREAM_API_KEY:', !!process.env.REACT_APP_GETSTREAM_API_KEY);
console.log('- GETSTREAM_API_SECRET:', !!process.env.GETSTREAM_API_SECRET);
console.log('- STREAM_API_SECRET:', !!process.env.STREAM_API_SECRET);
console.log('- GETSTREAM_SECRET:', !!process.env.GETSTREAM_SECRET);
console.log('- Final apiKey:', !!apiKey);
console.log('- Final apiSecret:', !!apiSecret);
console.log('- All env keys containing STREAM:', Object.keys(process.env).filter(key => key.includes('STREAM')));
console.log('- All env keys containing GETSTREAM:', Object.keys(process.env).filter(key => key.includes('GETSTREAM')));

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if API key and secret are available
    if (!apiKey || !apiSecret || !serverClient) {
      console.error('Error: GetStream configuration missing:');
      console.error('- apiKey present:', !!apiKey);
      console.error('- apiSecret present:', !!apiSecret);
      console.error('- serverClient initialized:', !!serverClient);

      return res.status(500).json({
        error: 'Server configuration error',
        details: {
          apiKey: !!apiKey,
          apiSecret: !!apiSecret,
          serverClient: !!serverClient
        }
      });
    }

    // Get the user ID from the request body
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    // Return the token
    return res.status(200).json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    return res.status(500).json({ error: 'Failed to generate token' });
  }
}
