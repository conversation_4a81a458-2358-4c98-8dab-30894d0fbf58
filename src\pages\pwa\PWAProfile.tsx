import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/use-profile';
import { useTasks } from '@/hooks/use-tasks';
import ProfileAbout from '@/components/profile/ProfileAbout';
import ProfileBadges from '@/components/profile/ProfileBadges';
import { organizationService } from '@/services/organizationService';
import { Organization } from '@/types/organization';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import {
  Edit,
  LogOut,
  HelpCircle,
  MessageSquare,
  Settings,
  Bell,
  Info,
  FileText,
  Mail
} from 'lucide-react';

const PWAProfile = () => {
  const { user } = useAuth();
  const { profile, isLoading } = useProfile(user?.id);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('about');
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoadingOrg, setIsLoadingOrg] = useState(false);
  const navigate = useNavigate();

  // Get tasks created by this user
  const { getTasksByUserId, isLoadingUserTasks } = useTasks();
  const { data: userTasks } = getTasksByUserId(user?.id);

  // Ensure activeTab is valid after removing tabs
  useEffect(() => {
    // Only 'about' tab is valid now
    if (activeTab !== 'about') {
      setActiveTab('about');
    }
  }, [activeTab]);

  // Fetch organization details if user is part of an organization
  useEffect(() => {
    if (user && profile) {
      setIsLoadingOrg(true);

      // Prioritize database values over metadata
      const userRole = profile?.role;
      const hasOrgId = !!profile?.organization_id;

      // Debug information
      console.log('Profile organization_id:', profile?.organization_id);
      console.log('Profile role:', profile?.role);

      // If profile has organization_id, fetch the actual organization from the database
      if (hasOrgId) {
        // Fetch the actual organization data
        organizationService.getOrganization(profile.organization_id)
          .then(orgData => {
            console.log('Fetched organization data:', orgData);
            setOrganization(orgData);
            setIsLoadingOrg(false);
          })
          .catch(error => {
            console.error('Error fetching organization:', error);
            // If fetching fails, create a mock org as fallback
            const mockOrg = {
              id: profile.organization_id,
              name: 'Your School',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setOrganization(mockOrg);
            setIsLoadingOrg(false);
            toast({
              variant: "destructive",
              title: "Error loading organization",
              description: "Could not load organization details.",
            });
          });
      } else if (userRole) {
        // If user has a role but no organization, create a mock one
        const mockOrg = {
          id: crypto.randomUUID(),
          name: 'Your School',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setOrganization(mockOrg);
      }

      setIsLoadingOrg(false);
    }
  }, [user, profile]);



  if (isLoading) {
    return (
      <PWAMobileLayout>
        <div className="container mx-auto px-4 py-4">
          <div className="space-y-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center">
                  <Skeleton className="h-16 w-16 rounded-full" />
                  <Skeleton className="h-5 w-32 mt-3" />
                  <Skeleton className="h-4 w-24 mt-2" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <Skeleton className="h-5 w-32 mb-3" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          </div>
        </div>
      </PWAMobileLayout>
    );
  }

  if (!profile || !user) {
    return (
      <PWAMobileLayout>
        <div className="container mx-auto px-4 py-4">
          <Card>
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">User Not Found</h2>
              <p className="text-gray-600">The user profile couldn't be loaded. Please try again later.</p>
              <Button
                onClick={() => navigate('/')}
                className="mt-4"
              >
                Return to Home
              </Button>
            </CardContent>
          </Card>
        </div>
      </PWAMobileLayout>
    );
  }

  const userFullName = profile.first_name && profile.last_name
    ? `${profile.first_name} ${profile.last_name}`
    : profile.first_name || "User";

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="space-y-4">
          {/* Profile Header */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col items-center text-center py-4">
                <Avatar className="h-24 w-24 mb-4">
                  {profile.avatar_url ? (
                    <AvatarImage src={profile.avatar_url} alt={userFullName} />
                  ) : (
                    <AvatarFallback className="text-2xl bg-classtasker-blue text-white">
                      {userFullName.charAt(0)}
                    </AvatarFallback>
                  )}
                </Avatar>
                <h2 className="text-xl font-bold">{userFullName}</h2>
                <p className="text-gray-600">{profile.job_title || (profile.account_type === 'school' ? 'School Staff' : 'Supplier')}</p>

                {profile.location && (
                  <div className="flex items-center mt-1 text-sm text-gray-600">
                    <span>{profile.location}</span>
                  </div>
                )}

                <Button
                  className="mt-4 w-full"
                  size="sm"
                  onClick={() => navigate('/profile/edit')}
                >
                  <Edit size={16} className="mr-2" /> Edit Profile
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Badges & Achievements */}
          <ProfileBadges
            userId={profile.id}
            accountType={profile.account_type || 'school'}
            tasksCreated={userTasks?.length || 0}
            tasksCompleted={userTasks?.filter(task => task.status === 'completed').length || 0}

            memberSince={profile.created_at}
          />

          {/* Organization Info */}
          {(organization || profile?.role || profile?.organization_id) && (
            <Card>
              <CardContent className="p-4">
                <h3 className="text-lg font-semibold mb-2">Organization</h3>
                <p className="text-gray-600 mb-1">{organization?.name || 'Your School'}</p>
                {organization?.address && (
                  <p className="text-gray-600 text-sm">{organization.address}</p>
                )}
                {organization?.city && organization?.state && (
                  <p className="text-gray-600 text-sm">
                    {organization.city}, {organization.state} {organization.zip}
                  </p>
                )}
                {profile?.role && (
                  <div className="mt-2 pt-2 border-t">
                    <p className="text-sm">
                      <span className="text-gray-500">Role:</span>{' '}
                      <span className="font-medium capitalize">{profile.role}</span>
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* About Section */}
          <ProfileAbout profile={profile} userTasks={userTasks} />

          {/* Help & Support Section */}
          <Card>
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold mb-3">Help & Support</h3>

              <div className="space-y-3">
                <Link to="/help" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <HelpCircle className="h-5 w-5 text-classtasker-blue mr-3" />
                  <div>
                    <p className="font-medium">Help Center</p>
                    <p className="text-sm text-gray-500">Guides and answers to common questions</p>
                  </div>
                </Link>

                <Link to="/contact" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <MessageSquare className="h-5 w-5 text-classtasker-blue mr-3" />
                  <div>
                    <p className="font-medium">Contact Support</p>
                    <p className="text-sm text-gray-500">Get help with any issues</p>
                  </div>
                </Link>

                <a href="mailto:<EMAIL>" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <Mail className="h-5 w-5 text-classtasker-blue mr-3" />
                  <div>
                    <p className="font-medium">Email Us</p>
                    <p className="text-sm text-gray-500"><EMAIL></p>
                  </div>
                </a>

                <Link to="/terms" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <FileText className="h-5 w-5 text-classtasker-blue mr-3" />
                  <div>
                    <p className="font-medium">Terms & Privacy</p>
                    <p className="text-sm text-gray-500">Review our terms and privacy policy</p>
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Account Actions */}
          <Card>
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold mb-3">Account</h3>

              <div className="space-y-3">
                <Link to="/settings" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <Settings className="h-5 w-5 text-gray-600 mr-3" />
                  <p className="font-medium">Settings</p>
                </Link>

                <Link to="/notifications" className="flex items-center p-2 hover:bg-gray-50 rounded-md">
                  <Bell className="h-5 w-5 text-gray-600 mr-3" />
                  <p className="font-medium">Notifications</p>
                </Link>

                <button
                  onClick={() => {
                    supabase.auth.signOut().then(() => {
                      navigate('/login');
                    });
                  }}
                  className="flex items-center p-2 w-full text-left hover:bg-gray-50 rounded-md text-red-600"
                >
                  <LogOut className="h-5 w-5 mr-3" />
                  <p className="font-medium">Sign Out</p>
                </button>
              </div>
            </CardContent>
            <CardFooter className="px-4 py-3 text-center text-xs text-gray-500 border-t">
              <div className="w-full flex flex-col items-center">
                <p>ClassTasker v1.0.0</p>
                <p className="mt-1">© 2025 ClassTasker Ltd. All rights reserved.</p>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAProfile;
